@echo off

REM === Check for container runtime
set _command=docker
where /q %_command%
if errorlevel 1 (
  set _command=podman
)
echo Runtime to be used: %_command%
REM ==============

set tag=latest
set image=mcr.microsoft.com/dotnet/aspire-dashboard

echo %image%:%tag% will be used

echo Starting dashboard

%_command% run ^
  --rm -it ^
  -p 18888:18888 ^
  -p 4317:18889 ^
  --name aspire-dashboard ^
  %image%:%tag%
