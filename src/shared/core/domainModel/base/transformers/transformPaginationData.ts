import { PaginationModel } from '../models';

export interface GenericPaginationData {
  pageNumber?: number | null;
  pageSize?: number | null;
  count?: number | null;
  totalResults?: number | null;
}

export function transformPaginationData(
  paginationData?: GenericPaginationData
): PaginationModel {
  return {
    pageNumber: paginationData?.pageNumber || 0,
    pageSize: paginationData?.pageSize || 0,
    count: paginationData?.count || 0,
    totalResults: paginationData?.totalResults || 100
  };
}
