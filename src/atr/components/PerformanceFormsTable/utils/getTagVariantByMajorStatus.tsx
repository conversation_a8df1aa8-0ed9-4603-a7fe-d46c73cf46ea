import { TagVariant } from '@ot/onetalent-ui-kit';

export const getTagVariantByMajorStatus = (majorStatus: string): TagVariant => {
  switch (majorStatus) {
    case 'Self Assessment':
      return TagVariant.LightBlue;
    case 'Manager Assessment':
      return TagVariant.Supernova;
    case 'Dotted-Line Manager Endorsement':
      return TagVariant.Electric;
    case 'Rating Approval':
      return TagVariant.PacificBlue;
    case 'Completed':
      return TagVariant.Green;
    default:
      return TagVariant.Grey;
  }
};
