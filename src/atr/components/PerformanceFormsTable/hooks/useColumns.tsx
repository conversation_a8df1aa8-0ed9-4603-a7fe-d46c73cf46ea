import { useMemo } from 'react';

import { DataColumnProps } from '@epam/uui-core';
import { Tag } from '@ot/onetalent-ui-kit';
import dayjs from 'dayjs';

import { PerformanceFormModel } from '@/atr/domain';
import { CellContent, UserInfo, UserInfoSize } from '@/shared/components';
import { DD_MMM_YYYY_HH_mm_ss } from '@/shared/constants';

import { getTagVariantByMajorStatus } from '../utils';

export const useColumns = (): DataColumnProps<PerformanceFormModel>[] => {
  return useMemo(
    () => [
      {
        key: 'id',
        caption: 'ID',
        group: 'atr-form',
        render: (form) => <CellContent>{form.id}</CellContent>,
        isSortable: false,
        width: 120,
        minWidth: 100
      },
      {
        key: 'templateName',
        caption: 'Name',
        group: 'atr-template',
        render: (form) => <CellContent>{form.templateName}</CellContent>,
        isSortable: false,
        width: 200,
        minWidth: 150
      },
      {
        key: 'majorStatus',
        caption: 'Major',
        group: 'atr-form-status',
        render: (form) => (
          <CellContent>
            <Tag variant={getTagVariantByMajorStatus(form.majorStatus)}>
              {form.majorStatus}
            </Tag>
          </CellContent>
        ),
        isSortable: false,
        width: 180,
        minWidth: 150
      },
      {
        key: 'minorStatus',
        caption: 'Minor',
        group: 'atr-form-status',
        render: (form) => <CellContent>{form.minorStatus}</CellContent>,
        isSortable: false,
        width: 140,
        minWidth: 120
      },
      {
        key: 'employeeId',
        caption: 'ID',
        group: 'employee',
        render: (form) => <CellContent>{form.employeeId}</CellContent>,
        isSortable: false,
        width: 120,
        minWidth: 100
      },
      {
        key: 'employeeFullName',
        caption: 'Full Name',
        group: 'employee',
        render: (form) => (
          <CellContent>
            {form.employeeFullName ? (
              <UserInfo
                size={UserInfoSize.ExtraSmall}
                fullName={form.employeeFullName}
                titleClassName="text-body-2-regular"
                hasTooltip
              />
            ) : (
              ''
            )}
          </CellContent>
        ),
        isSortable: false,
        width: 200,
        minWidth: 180
      },
      {
        key: 'employeeCompanyName',
        caption: 'Company',
        group: 'employee',
        render: (form) => <CellContent>{form.employeeCompanyName}</CellContent>,
        isSortable: false,
        width: 150,
        minWidth: 120
      },
      {
        key: 'employeeDirectorate',
        caption: 'Directorate',
        group: 'employee',
        render: (form) => <CellContent>{form.employeeDirectorate}</CellContent>,
        isSortable: false,
        width: 130,
        minWidth: 110
      },
      {
        key: 'employeeFunction',
        caption: 'Function',
        group: 'employee',
        render: (form) => <CellContent>{form.employeeFunction}</CellContent>,
        isSortable: false,
        width: 120,
        minWidth: 100
      },
      {
        key: 'employeeDivision',
        caption: 'Division',
        group: 'employee',
        render: (form) => <CellContent>{form.employeeDivision}</CellContent>,
        isSortable: false,
        width: 120,
        minWidth: 100
      },
      {
        key: 'atrGroupName',
        caption: 'Name',
        group: 'atr-group',
        render: (form) => <CellContent>{form.atrGroupName}</CellContent>,
        isSortable: false,
        width: 120,
        minWidth: 100
      },
      {
        key: 'assessmentLineManagerId',
        caption: 'ID',
        group: 'assessment-manager-lm',
        render: (form) => (
          <CellContent>{form.assessmentLineManagerId}</CellContent>
        ),
        isSortable: false,
        width: 120,
        minWidth: 100
      },
      {
        key: 'assessmentLineManagerName',
        caption: 'Full Name',
        group: 'assessment-manager-lm',
        render: (form) => (
          <CellContent>
            {form.assessmentLineManagerName ? (
              <UserInfo
                size={UserInfoSize.ExtraSmall}
                fullName={form.assessmentLineManagerName}
                titleClassName="text-body-2-regular"
                hasTooltip
              />
            ) : (
              ''
            )}
          </CellContent>
        ),
        isSortable: false,
        width: 180,
        minWidth: 150
      },
      {
        key: 'assessmentB2BManagerId',
        caption: 'ID',
        group: 'assessment-manager-b2b',
        render: (form) => (
          <CellContent>{form.assessmentB2BManagerId}</CellContent>
        ),
        isSortable: false,
        width: 120,
        minWidth: 100
      },
      {
        key: 'assessmentB2BManagerName',
        caption: 'Full Name',
        group: 'assessment-manager-b2b',
        render: (form) => (
          <CellContent>
            {form.assessmentB2BManagerName ? (
              <UserInfo
                size={UserInfoSize.ExtraSmall}
                fullName={form.assessmentB2BManagerName}
                titleClassName="text-body-2-regular"
                hasTooltip
              />
            ) : (
              ''
            )}
          </CellContent>
        ),
        isSortable: false,
        width: 180,
        minWidth: 150
      },
      {
        key: 'dottedLineManagerId',
        caption: 'ID',
        group: 'dotted-line-manager',
        render: (form) => <CellContent>{form.dottedLineManagerId}</CellContent>,
        isSortable: false,
        width: 120,
        minWidth: 100
      },
      {
        key: 'dottedLineManagerName',
        caption: 'Full Name',
        group: 'dotted-line-manager',
        render: (form) => (
          <CellContent>
            {form.dottedLineManagerName ? (
              <UserInfo
                size={UserInfoSize.ExtraSmall}
                fullName={form.dottedLineManagerName}
                titleClassName="text-body-2-regular"
                hasTooltip
              />
            ) : (
              ''
            )}
          </CellContent>
        ),
        isSortable: false,
        width: 180,
        minWidth: 150
      },
      {
        key: 'lastUpdated',
        caption: 'Time & Date',
        group: 'last-updated',
        render: (form) => (
          <CellContent>
            {form.lastUpdated
              ? dayjs(form.lastUpdated).format(DD_MMM_YYYY_HH_mm_ss)
              : ''}
          </CellContent>
        ),
        isSortable: false,
        width: 140,
        minWidth: 120
      },
      {
        key: 'updatedBy',
        caption: 'User Name',
        group: 'last-updated-by',
        render: (form) => <CellContent>{form.updatedBy}</CellContent>,
        isSortable: false,
        width: 150,
        minWidth: 120
      }
    ],
    []
  );
};
