import { FC } from 'react';

import { DEFAULT_SORTING, usePerformanceForms } from '@/atr/domain';
import { PerformanceDataTable, useDataSourceState } from '@/shared/components';
import {
  DEFAULT_PAGE_NUMBER,
  DEFAULT_PAGE_SIZE,
  DEFAULT_VISIBLE_COUNT
} from '@/shared/constants/pagination';

import { useColumnGroups, useColumns } from './hooks';
import { PerformanceFormsEmpty } from './PerformanceFormsEmpty';
import { PerformanceFormsError } from './PerformanceFormsError';

export interface PerformanceFormsTableProps {
  emptyScreenSubtitle?: string;
  className?: string;
}

export const PerformanceFormsTable: FC<PerformanceFormsTableProps> = ({
  className
}) => {
  const { dataSource, parameters: pagination } = useDataSourceState<string>({
    pageSize: DEFAULT_PAGE_SIZE,
    initialPageNumber: DEFAULT_PAGE_NUMBER
  });

  const queryResult = usePerformanceForms({
    pageSize: pagination.pageSize ?? DEFAULT_PAGE_SIZE,
    pageNumber: pagination.pageNumber ?? DEFAULT_PAGE_NUMBER,
    orderBy: DEFAULT_SORTING
  });

  const columns = useColumns();
  const columnGroups = useColumnGroups();

  return (
    <div data-attributes="PerformanceFormsTable" className={className}>
      <PerformanceDataTable
        dataAttributes="PerformanceFormsTable"
        {...dataSource}
        showContainer
        styles={{
          minHeight: !queryResult.data?.items.length ? 440 : 0
        }}
        queryResult={queryResult}
        columns={columns}
        columnGroups={columnGroups}
        renderNoResults={PerformanceFormsEmpty}
        renderError={PerformanceFormsError}
        paginationSize="24"
      />
    </div>
  );
};
