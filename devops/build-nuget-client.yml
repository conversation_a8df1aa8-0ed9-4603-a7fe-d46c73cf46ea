trigger:
  branches:
    include:
      - main
  paths:
    include:
      - src/WebAPI.Client/*
    exclude:
      - devops/*

resources:
  repositories:
    - repository: devops-common
      type: git
      name: OneTalent/devops-common
      ref: main

stages:
- template: pipelines/templates/nuget-build2.template.yml@devops-common
  parameters:
    project_name: "WebAPI.Client"
    project_folder: "src/WebAPI.Client/"
    api_client_enabled: true
    api_client_web_app_path: "src/WebAPI"