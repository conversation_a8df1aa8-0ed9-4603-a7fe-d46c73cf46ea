<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <clear />
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    <add key="OneTalent-NuGet" value="https://devopsad.pkgs.visualstudio.com/40fc911e-43e1-4746-bf4c-af2ba93b4046/_packaging/OneTalent-NuGet/nuget/v3/index.json" />
    <add key="OneTalent-WaaS" value="https://devopsad.pkgs.visualstudio.com/_packaging/ADNOC.WaaS%40Local/nuget/v3/index.json" />
  </packageSources>
  <packageSourceCredentials>
    <OneTalent-NuGet>
      <add key="Username" value="irrelevant" />
      <add key="ClearTextPassword" value="%ADNOC_FEED_TOKEN%" />
    </OneTalent-NuGet>
    <OneTalent-WaaS>
      <add key="Username" value="irrelevant" />
      <add key="ClearTextPassword" value="%ADNOC_FEED_TOKEN%" />
    </OneTalent-WaaS>
  </packageSourceCredentials>
  <packageSourceMapping>
    <packageSource key="nuget.org">
      <package pattern="*" />
    </packageSource>
    <packageSource key="OneTalent-NuGet">
      <package pattern="OneTalent.*" />
    </packageSource>
    <packageSource key="OneTalent-WaaS">
      <package pattern="eServices.Workflow.*" />
    </packageSource>
  </packageSourceMapping>
</configuration>